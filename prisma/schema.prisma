generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model users {
  id                        String                     @id @default(cuid()) @db.Var<PERSON>har(36)
  username                  String                     @unique
  email                     String                     @unique
  password_hash             String
  created_at                DateTime                   @default(now())
  updated_at                DateTime                   @updatedAt

  landingContactSubmissions LandingContactSubmission[]
  landingServices           LandingService[]
  landingProfileData        LandingProfileData[]
  landingTestimonials       LandingTestimonial[]
  
 
}

model profiles {
  user_id      String   @unique @db.VarChar(36)
  display_name String?
  bio          String?
  avatar_url   String?
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt
}

model links {
  id            String   @id @default(cuid()) @db.VarChar(36)
  user_id       String   @db.VarChar(36)
  label         String   @db.VarChar(255)
  url           String
  icon          String?
  display_order Int      @default(0)
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt
}

model PasswordResetToken {
  id        String    @id @default(cuid()) @db.VarChar(36)
  user    String    @db.VarChar(36)
  tokenHash String    @unique
  expiresAt DateTime
  usedAt    DateTime?
  createdAt DateTime  @default(now())

  @@index([userId])
  @@index([expiresAt])
}

model LandingSkillCategory {
  category_id     String @unique @db.VarChar(36)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  landing_page_id String   @db.VarChar(36)

  @@index([category_id])  
}

model LandingSkill {
  category_id     String   @db.VarChar(36)
  skill_id        String   @db.VarChar(36)
  level           Int
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  id              String   @id @default(cuid()) @db.VarChar(36)
  landing_page_id String   @db.VarChar(36)

  @@index([category_id])
  @@index([skill_id])
}

model SkillCategorys {
  title      String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  id         String   @id @default(cuid()) @db.VarChar(36)
}

model Skill {
  category_id String   @db.VarChar(36)
  name        String
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
  id          String   @id @default(cuid()) @db.VarChar(36)

  @@index([category_id])
}

model Project {
  id           String        @id @default(cuid()) @db.VarChar(36)
  page_id      String        @db.VarChar(36)
  title        String        @db.VarChar(255)
  description  String        @db.Text
  isActive     Boolean       @default(true) @map("is_active")
  createdAt    DateTime      @default(now()) @map("created_at")
  updatedAt    DateTime      @updatedAt @map("updated_at")
  projectCards ProjectCard[]
  landingProfileData         LandingProfileData @relation(fields: [page_id], references: [id], onDelete: Cascade) 
  
  @@index([page_id], map: "fk_projects_users_idx")  
  @@map("projects")
}

model ProjectCard {
  id          String   @id @default(cuid()) @db.VarChar(36)
  project_id  String   @db.VarChar(36)
  no          Int      @default(0)
  title       String   @db.VarChar(255)
  description String   @db.Text
  image       String   @db.VarChar(2048)
  liveUrl     String?  @map("live_url") @db.VarChar(2048)
  githubUrl   String?  @map("github_url") @db.VarChar(2048)
  tags        String?  @db.Text
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  project     Project  @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@index([project_id], map: "fk_project_cards_projects_idx")
  @@index([no], map: "idx_project_cards_order")
  @@map("project_cards")
}

model LandingTestimonial {
  id           String   @id @default(cuid()) @db.VarChar(36)
  user_id      String   @db.VarChar(36)
  client_name  String   @db.VarChar(255)
  client_title String?  @db.VarChar(255)
  client_image String?  @db.VarChar(2048)
  testimonial  String   @db.Text
  rating       Int?     @default(5)
  displayOrder Int      @default(0) @map("display_order")
  isActive     Boolean  @default(true) @map("is_active")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  user         users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id], map: "fk_testimonials_users_idx")
  @@index([displayOrder], map: "idx_testimonials_order")
  @@map("landing_testimonials")
}

model LandingContactSubmission {
  id        String   @id @default(cuid()) @db.VarChar(36)
  user_id   String   @db.VarChar(36)
  name      String   @db.VarChar(255)
  email     String   @db.VarChar(255)
  subject   String?  @db.VarChar(255)
  message   String   @db.Text
  isRead    Boolean  @default(false) @map("is_read")
  createdAt DateTime @default(now()) @map("created_at")
  user      users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id], map: "fk_contact_submissions_users_idx")
  @@index([isRead], map: "idx_contact_submissions_read")
  @@index([createdAt], map: "idx_contact_submissions_date")
  @@map("landing_contact_submissions")
}

model LandingService {
  id           String   @id @default(cuid()) @db.VarChar(36)
  user_id      String   @db.VarChar(36)
  title        String   @db.VarChar(255)
  description  String   @db.Text
  icon         String?  @db.VarChar(255)
  price        String?  @db.VarChar(100)
  features     String?  @db.Text
  displayOrder Int      @default(0) @map("display_order")
  isActive     Boolean  @default(true) @map("is_active")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  user         users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id], map: "fk_services_users_idx")
  @@index([displayOrder], map: "idx_services_order")
  @@map("landing_services")
}

model LandingProfileData {
  id         String   @id @default(cuid()) @db.VarChar(36)
  user_id    String   @db.VarChar(36)
  name       String
  title      String
  bio        String?  @db.Text
  about      String?  @db.Text
  journey    String?  @db.Text
  email      String
  phone      String
  location   String
  github     String
  linkedin   String
  twitter    String
  image      String?
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  user       users     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  projects   Project[]

  @@index([user_id], map: "fk_profile_data_users_idx")
  @@map("landing_profile_data")
}
